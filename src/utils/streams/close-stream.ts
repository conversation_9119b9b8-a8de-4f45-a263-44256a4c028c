import type { ClientReadableStream } from '@grpc/grpc-js'
import { StreamError } from '../../errors'
import { createDeferredWithTimeout } from '../promises'

export async function closeStream(stream: ClientReadableStream<unknown>, timeout = 10_000, destroyOnFail = true) {
    if (stream.closed || stream.destroyed || stream.readableEnded) {
        return
    }

    const promise = createDeferredWithTimeout<void>(timeout, () => new StreamError('Stream close timeout'))
    const closeHandler = () => !promise.isSettled && promise.resolve()
    const errorHandler = (error: unknown) => !promise.isSettled && promise.reject(error)

    stream.once('close', closeHandler)
    stream.once('error', errorHandler)
    stream.cancel()

    const handleError = (error: Error) => {
        if (destroyOnFail) {
            stream.destroy()
        } else {
            throw error
        }
    }

    return promise.catch(handleError).finally(() => {
        stream.removeListener('close', closeHandler)
        stream.removeListener('error', errorHandler)
    })
}
